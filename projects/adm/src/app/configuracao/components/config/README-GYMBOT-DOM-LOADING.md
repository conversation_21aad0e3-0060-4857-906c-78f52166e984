# Melhoria no Loading da Grid Gymbot - Manipulação DOM

## Problema Identificado

A grid de departamentos Gymbot apresentava problemas onde:
- Após adicionar um departamento, não havia feedback visual consistente
- Após excluir um departamento, a tabela não mostrava loading adequado
- O usuário não tinha certeza se a operação estava sendo processada

## Solução Implementada

### 1. Manipulação Direta do DOM

Implementamos uma solução similar ao `esconderPactoRelatorioRenderer()` que manipula diretamente o DOM para controlar o loading da grid Gymbot.

### 2. Variáveis de Controle

```typescript
carregandoGymbot: boolean = false;
elementoGymbot: HTMLElement;
spinnerGymbot: HTMLElement;
```

### 3. Método de Controle de Loading

```typescript
esconderPactoRelatorioRendererGymbot() {
    // Busca especificamente o renderer da grid Gymbot
    const allRenderers = document.querySelectorAll("pacto-relatorio-renderer");
    
    // Encontra o renderer correto (último renderer ou por contexto específico)
    this.elementoGymbot = null;
    allRenderers.forEach((renderer: HTMLElement) => {
        const parent = renderer.closest('[data-grid="gymbot"]') || 
                       renderer.closest('.grid-gymbot') ||
                       // Fallback: pega o último renderer
                       (allRenderers.length > 1 ? allRenderers[allRenderers.length - 1] as HTMLElement : renderer as HTMLElement);
        if (parent === renderer.closest('[data-grid="gymbot"]') || 
            parent === renderer.closest('.grid-gymbot') ||
            renderer === allRenderers[allRenderers.length - 1]) {
            this.elementoGymbot = renderer as HTMLElement;
        }
    });

    if (this.carregandoGymbot) {
        // Esconde o componente original
        if (this.elementoGymbot) {
            this.elementoGymbot.style.display = "none";
        }

        // Cria e exibe o spinner
        if (!this.spinnerGymbot) {
            this.spinnerGymbot = document.createElement("div");
            this.spinnerGymbot.style.display = "flex";
            this.spinnerGymbot.style.flexDirection = "column";
            this.spinnerGymbot.style.alignItems = "center";
            this.spinnerGymbot.style.justifyContent = "center";
            this.spinnerGymbot.style.height = "170px";
            this.spinnerGymbot.style.width = "100%";
            this.spinnerGymbot.style.backgroundColor = "#f9f9f9";
            this.spinnerGymbot.style.border = "1px solid #f1f1f1";
            this.spinnerGymbot.style.borderRadius = "4px";
            this.spinnerGymbot.innerHTML = `
                <img src="${this.treinoFrontURL}/pt/assets/images/gif/loading-pacto.gif" alt="Carregando..." style="width: 64px; height: 64px; margin-bottom: 12px;" />
                <span style="font-size: 14px; color: #555;">Carregando departamentos Gymbot...</span>
            `;

            if (this.elementoGymbot && this.elementoGymbot.parentElement) {
                this.elementoGymbot.parentElement.insertBefore(this.spinnerGymbot, this.elementoGymbot);
            }
        }

        this.spinnerGymbot.style.display = "flex";
    } else {
        // Mostra o componente original
        if (this.elementoGymbot) {
            this.elementoGymbot.style.display = "block";
        }

        // Esconde o spinner
        if (this.spinnerGymbot) {
            this.spinnerGymbot.style.display = "none";
        }
    }
}
```

### 4. Implementação na Ação de Excluir

```typescript
actionFn: (data) => {
    const id_departamento = data.row.id_departamento;
    
    // Inicia o loading
    this.carregandoGymbot = true;
    this.esconderPactoRelatorioRendererGymbot();
    
    this.apiConfigIA
        .removerInstrucoesGymbot(this.empresaSelecionada, [id_departamento])
        .pipe(
            tap(() => {
                this.notificationService.success("Departamento excluído com sucesso.");
            }),
            switchMap(() => timer(1000)), // Delay para mostrar o loading
            switchMap(() => this.apiConfigIA.obterInstrucoesGymbot(this.empresaSelecionada)),
            tap((response) => {
                const content = Array.isArray(response) && response.length > 0 ? response : [];
                this.formControlGymbotData.setValue({ content });
            }),
            catchError((error) => {
                this.notificationService.error("Erro ao remover Departamento: " + error.message);
                return of([]);
            })
        )
        .subscribe({
            next: () => {
                // Para o loading
                this.carregandoGymbot = false;
                this.esconderPactoRelatorioRendererGymbot();
            },
            error: () => {
                // Para o loading mesmo em caso de erro
                this.carregandoGymbot = false;
                this.esconderPactoRelatorioRendererGymbot();
            }
        });
    
    return of(true);
}
```

### 5. Implementação no Botão de Adicionar

```typescript
onClick: () => {
    // Validações...
    
    // Inicia o loading
    this.carregandoGymbot = true;
    this.esconderPactoRelatorioRendererGymbot();
    
    this.apiConfigIA
        .enviarConfiguracaoGymbot(dto)
        .pipe(
            tap(() => {
                this.notificationService.success("Departamento salvo com sucesso.");
                // Reset dos formulários
            }),
            switchMap(() => timer(1000)), // Delay para mostrar o loading
            switchMap(() => this.apiConfigIA.obterInstrucoesGymbot(this.empresaSelecionada)),
            tap((response) => {
                this.formControlGymbotData.setValue({
                    content: response.length > 0 ? response : [],
                });
            }),
            catchError((error) => {
                this.notificationService.error("Erro ao salvar departamento: " + error.message);
                return of([]);
            })
        )
        .subscribe({
            next: () => {
                // Para o loading
                this.carregandoGymbot = false;
                this.esconderPactoRelatorioRendererGymbot();
            },
            error: () => {
                // Para o loading mesmo em caso de erro
                this.carregandoGymbot = false;
                this.esconderPactoRelatorioRendererGymbot();
            }
        });
}
```

## Benefícios da Solução

### ✅ **Loading Visual Consistente**
- Spinner animado durante todas as operações
- Feedback visual claro para o usuário
- Mesmo padrão usado em outras partes do sistema

### ✅ **Controle Preciso**
- Manipulação direta do DOM garante controle total
- Loading é exibido exatamente quando necessário
- Funciona independente do ciclo de vida do Angular

### ✅ **Experiência do Usuário Melhorada**
- Usuário sempre sabe quando uma operação está em andamento
- Feedback imediato após ações
- Transições suaves entre estados

### ✅ **Robustez**
- Funciona mesmo com múltiplos renderers na página
- Tratamento de erro adequado
- Fallback para encontrar o elemento correto

## Como Testar

1. **Adicionar Departamento:**
   - Preencha os campos e clique em "Salvar Departamento"
   - Observe o loading com spinner animado
   - Verifique se o novo item aparece após o carregamento

2. **Excluir Departamento:**
   - Clique no ícone de excluir em qualquer item
   - Observe o loading com spinner animado
   - Verifique se o item foi removido após o carregamento

3. **Múltiplas Operações:**
   - Teste operações consecutivas
   - Verifique se o loading funciona corretamente em cada operação

## Vantagens desta Abordagem

- **Simplicidade**: Usa o mesmo padrão já estabelecido no sistema
- **Confiabilidade**: Manipulação direta do DOM é mais previsível
- **Consistência**: Mesmo visual e comportamento de outras partes do sistema
- **Performance**: Não depende do ciclo de detecção de mudanças do Angular
