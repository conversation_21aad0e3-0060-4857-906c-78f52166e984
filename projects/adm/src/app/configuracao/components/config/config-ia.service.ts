import { EventEmitter, Injectable } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { PactoModalSize } from "@base-core/modal/modal.service";
import {
	ConfiguracaoCrm,
	CrmApiConfigIAService,
	CrmApiConfiguracaoService,
	CrmApiFaseService,
	FaseVO,
} from "crm-api";
import { SnotifyService } from "ng-snotify";
import { forkJoin, from, of, throwError, timer } from "rxjs";
import { catchError, delay, switchMap, tap } from "rxjs/operators";
import { ClientDiscoveryService, SessionService } from "sdk";
import {
	DialogService,
	LoaderService,
	PactoDataGridColumnConfigDto,
	PactoDataGridConfig,
	PactoModalRef,
} from "ui-kit";
import { CampanhaDialogComponent } from "../dialog/campanha/campanha-dialog.component";
import { FaseDialogComponent } from "../dialog/fase/fase.dialog.component";
import { QrCodeDialogComponent } from "../dialog/qrcode/qr-code-dialog.component";
import { ConfigItemType } from "../inputs/form-configuracoes.model";
import { tokenInstanceValidator } from "../inputs/validators/token-instance.validator";
import {
	ConfiguracaoCrmFaseIA,
	ConfiguracaoIA,
	MaladiretaEntity,
	NotificationAt,
	NotificationBeforeAfter,
	TypedNotification,
} from "./model/config-crm-ia.model";
import { ConfigModuloSubGroup } from "./model/config-module-subgroup.model";
import { ModeloPrompts } from "./model/modelo-prompts";
import { SubGrupoInputs } from "./model/sub-group-inputs.model";

@Injectable({
	providedIn: "root",
})
export class ConfigIaService {
	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private crmConfigApiService: CrmApiConfiguracaoService,
		private apiFases: CrmApiFaseService,
		private apiConfigIA: CrmApiConfigIAService,
		private dialogService: DialogService,
		private loaderService: LoaderService
	) {
		forkJoin(
			this.crmConfigApiService.consultar(),
			this.apiFases.consultar(),
			this.apiConfigIA.consultarConfiguracao(),
			this.apiConfigIA.consultarConfiguracaoDeFases(),
			this.apiConfigIA.consultarConfiguracaoDeFasesExtrasIA()
		).subscribe(
			([config, fases, configuracaoIA, configuracaoFasesAi, fasesExtraIA]) => {
				this.config = config;
				this.fases = fases || [];
				this.configuracaoIA = configuracaoIA as any;
				this.configuracaoFasesAi = configuracaoFasesAi;
				this.fasesExtraIA = fasesExtraIA;
			},
			(error) => {
				this.notificationService.error(
					"Falha ao carregar configurações da api " + error.url
				);
				console.error("Falha ao consultar configurações", error);
			},
			() => {
				this.configLoaded = true;
				this.prepareDetectChanges();
				this.loaded.emit(true);
			}
		);
		this.formTelefoneConectado.valueChanges.subscribe((valor) => {
			this.linkWhatsApp = valor
				? `https://api.whatsapp.com/send?phone=${valor}`
				: "";
			if (this.configLoaded) {
				this.reloaded.emit(true);
			}
		});
		this.validators();

		this.treinoFrontURL = this.discoveryService.getUrlMap().treinoFrontUrl;
	}

	carregandoRegua: boolean = false;
	elemento: HTMLElement;
	spinner: HTMLElement;
	intervalo: any;
	ultimoId: string = "";
	configuracaoIA: ConfiguracaoIA;
	configuracaoFasesAi: ConfiguracaoCrmFaseIA[];
	config: ConfiguracaoCrm;
	fases: FaseVO[];
	loaded: EventEmitter<boolean> = new EventEmitter();
	configLoaded: boolean = false;
	detectChanges: EventEmitter<boolean> = new EventEmitter();
	formGroupConfiguracaoIA = new FormGroup({});
	fasesExtraIA: MaladiretaEntity[] = [];
	formControlPersonalidade = new FormControl();
	formHabilitarPactoConversas = new FormControl();
	formControlLoginPactoConversas: FormControl = new FormControl();
	formControlSenhaPactoConversas: FormControl = new FormControl();
	formControlTokenPactoConversas: FormControl = new FormControl();
	formControlInstanciaPactoConversas: FormControl = new FormControl();
	formControlFaseModal: FormControl = new FormControl();
	formControlWhatsAppBusiness: FormControl = new FormControl();
	formControlInformacoesAdicionaisAcademia = new FormControl();
	horarioPadraoAnterior: string;
	formControlHorarioPadrao: FormControl = new FormControl();
	empresaSelecionada: any;
	inputFasesExtras = [];
	reloaded: EventEmitter<boolean> = new EventEmitter();
	formEmailResponsavelConversasAI: FormControl = new FormControl();
	formTelefoneResponsavelConversasAI: FormControl = new FormControl();
	chavesBanco: any[] = [];
	chavesEmpresas: any[] = [];
	formControlBancoDaMatrizRede = new FormControl();
	formControlUnidadeDeMatrizRede = new FormControl();
	configRedeSelecionada: boolean = false;
	formControllConfigRedePactoConversas = new FormControl();
	guardarValorCodigoUnidadeMatriz: any;
	isCarregandoEmpresas: boolean = false;
	primeiraVez: boolean = true;
	formToke;
	formGroupConfiguracaoGymbot = new FormGroup({
		formControlToken: new FormControl(),
		formControlDepartamento: new FormControl(),
		formControlInstrucao: new FormControl(),
		habilitarGymbot: new FormControl(),
	});
	departamentosGymbot: any[] = [];
	departamentosGymbotVersion: number = 0; // Versão para forçar atualização do select
	codigoConfiguracao: number;

	formControlCampanhaBtn = new FormControl();
	formControlInputFile: FormControl = new FormControl();
	fromControlCampanhas = new FormControl({ content: [] });
	formControlMesmoDia = new FormControl({ content: [] });
	formControlDiaDoEvento = new FormControl({ content: [] });
	formControlDiasIntermediarios = new FormControl({ content: [] });
	formControlTabelaDeReguaDeAtendimento = new FormControl({ content: [] });
	formControlDescricaoContatoProativo: FormControl = new FormControl();
	formControlDescricaoReguaAtendimento: FormControl = new FormControl();
	formTelefoneConectado: FormControl = new FormControl();
	formControlDesabilitarAgendamentoAulaExperimental: FormControl =
		new FormControl();

	private readonly horarioDefault: string = "11:00";
	linkWhatsApp: string = "";
	treinoFrontURL: string;

	formTokenGymbot = new FormControl();
	formHabilitarGymbot = new FormControl();
	formControlGymbotData = new FormControl({ content: [] });

	intervaloPDF: any;
	consultandoPDFConversas: boolean = false;
	fileInputBackUp: HTMLElement | null = null;
	containerBackUp: HTMLElement | null = null;
	fasesDefault: any[] = [
		"CONVERSAO_LEAD",
		"CONVERSAO_INDICADOS",
		"CONVERSAO_AGENDADOS",
		"CONVERSAO_DESISTENTES",
		"CONVERSAO_VISITANTES_ANTIGOS",
		"CONVERSAO_EX_ALUNOS",
		"ULTIMAS_SESSOES",
		"AGENDAMENTOS_LIGACOES",
		"CONVERSAO_RECEPTIVO",
		"CRM_EXTRA",
		"SEM_AGENDAMENTO",
	];

	prepareDetectChanges() {
		[
			this.formControlLoginPactoConversas,
			this.formControlSenhaPactoConversas,
			this.formControlTokenPactoConversas,
			this.formControlInstanciaPactoConversas,
			this.formGroupConfiguracaoIA,
			this.formControllConfigRedePactoConversas,
			this.formControlBancoDaMatrizRede,
			this.formControlUnidadeDeMatrizRede,
		].forEach((formControl) => {
			from(formControl.valueChanges).subscribe(() => {
				this.detectChanges.emit(true);
			});
		});

		this.formControlBancoDaMatrizRede.valueChanges.subscribe((value) => {
			this.loaderService.initForce();
			this.chavesEmpresas.splice(0, this.chavesEmpresas.length);
			const chavesBanco = value || this.sessionService.chave;
			this.discoveryService.discover(chavesBanco, null, false).subscribe({
				next: (res) => {
					if (res.empresas && res.empresas.length > 0) {
						this.chavesEmpresas.push(
							...res.empresas.map((empresa) => ({
								id: empresa.codigo,
								label: empresa.codigo + " - " + empresa.nome,
							}))
						);
					}
				},
				error: (erro) => {
					console.error("Ocorreu um erro ao buscar empresas: ", erro);
				},
				complete: () => {
					this.formControlUnidadeDeMatrizRede.reset();
					if (this.primeiraVez && this.guardarValorCodigoUnidadeMatriz) {
						this.formControlUnidadeDeMatrizRede.setValue(
							this.guardarValorCodigoUnidadeMatriz
						);
					}
					this.primeiraVez = false;
					this.loaderService.stopForce();
				},
			});
		});
		this.formHabilitarPactoConversas.markAsTouched();
		this.formHabilitarPactoConversas.markAsDirty();
		this.detectChanges.emit(true);
	}

	updateFormControls(config: any, configFases: any, configFasesExtra: any) {
		this.loadConfiguracaoRede(config);
		this.loadConfiguracaoEmpresa(config);
		this.loadFases(configFases);
		this.loadFasesMetaExtra(configFasesExtra);
		this.loadConfiguracaoesGymbot(config);
		this.loadCampanhas(config);
		this.obterNotificacoesProativo();
		this.obterNotificacoesLead();
		this.loadDadosTelefoneConectado(config);
		if (this.configLoaded) {
			this.reloaded.emit(true);
		}
		this.detectChanges.emit(true);
		setTimeout(() => {
			this.iniciarVerificacaoStatusDePDF();
		}, 1000);
	}

	private loadConfiguracaoRede(config: any) {
		this.formControllConfigRedePactoConversas.setValue(
			config.configuracaoRede.tipoConfigRede
		);
		this.formControlBancoDaMatrizRede.setValue(
			config.configuracaoRede.chaveBancoMatriz
		);
		this.formControlUnidadeDeMatrizRede.setValue(
			config.configuracaoRede.codigoUnidadeMatriz
		);
		this.guardarValorCodigoUnidadeMatriz =
			config.configuracaoRede.codigoUnidadeMatriz;
	}

	private loadConfiguracaoEmpresa(config: any) {
		this.codigoConfiguracao = config.codigo;
		this.formHabilitarPactoConversas.setValue(config.habilitarconfigia);
		this.formControlLoginPactoConversas.setValue(config.loginPactoConversas);
		this.formControlSenhaPactoConversas.setValue(config.senhaPactoConversas);
		this.formControlPersonalidade.setValue(
			config.personalidade || ModeloPrompts["PERSONALIDADE_IA"] || ""
		);
		this.formControlInformacoesAdicionaisAcademia.setValue(
			config.informacoesAdicionaisAcademia ||
				ModeloPrompts["INFORMACOES_ADICIONAIS"] ||
				""
		);
		this.formControlTokenPactoConversas.setValue(config.tokenZApi);
		this.formControlInstanciaPactoConversas.setValue(config.idInstanciaZApi);
		this.formControlWhatsAppBusiness.setValue(config.whatsappBusiness);
		this.formEmailResponsavelConversasAI.setValue(
			config.emailResponsavelConversasAI
		);

		this.formTelefoneResponsavelConversasAI.setValue(
			config.telefoneResponsavelConversasAI
		);

		this.formControlDescricaoContatoProativo.setValue(
			config.descricaoNotificacaoProativo
		);
		const horario = config.horarioPadrao
			? config.horarioPadrao.substring(0, 5)
			: this.horarioDefault;
		this.formControlHorarioPadrao.setValue(horario);

		this.formTokenGymbot.setValue(config.tokenGymbot || "");
		this.formHabilitarGymbot.setValue(config.habilitarGymbot || false);

		this.formHabilitarPactoConversas.markAsTouched();
		this.formHabilitarPactoConversas.markAsDirty();
	}

	loadFases(configFases: any) {
		if (!this.fases) {
			this.fases = [];
		}
		this.fases = this.fases.filter(
			(fase) => !this.fasesDefault.includes(fase.name)
		);

		this.fases.forEach((fase) => {
			const habilitarControlName = fase.name + "_habilitar";
			const descricaoControlName = fase.name + "_descricao";
			const mensagensExtrasControlName = fase.name + "_mensagens_extras";

			if (configFases) {
				const faseConfig = configFases.find((f) => f.fase === fase.name) || {
					habilitar: false,
					descricao: "",
					mensagensExtras: "",
				};
				if (this.formGroupConfiguracaoIA.get(descricaoControlName)) {
					this.formGroupConfiguracaoIA
						.get(descricaoControlName)
						.setValue(
							faseConfig.habilitar
								? faseConfig.descricao
								: ModeloPrompts[fase.name] || ""
						);
				}
				if (this.formGroupConfiguracaoIA.get(mensagensExtrasControlName)) {
					this.formGroupConfiguracaoIA
						.get(mensagensExtrasControlName)
						.setValue(faseConfig.habilitar ? faseConfig.mensagensextras : "");
				}
				if (this.formGroupConfiguracaoIA.get(habilitarControlName)) {
					this.formGroupConfiguracaoIA
						.get(habilitarControlName)
						.setValue(false);
				}
			} else {
				this.formGroupConfiguracaoIA.get(descricaoControlName).setValue("");
				this.formGroupConfiguracaoIA.get(habilitarControlName).setValue(false);
				this.formGroupConfiguracaoIA
					.get(mensagensExtrasControlName)
					.setValue("");
			}
		});
	}

	loadFasesMetaExtra(configFasesExtra: any[]) {
		if (configFasesExtra && configFasesExtra.length > 0) {
			this.inputFasesExtras = [...this.fasesExtraIA];

			this.fasesExtraIA.forEach((fasesExtra) => {
				const habilitarControlName = `${fasesExtra.titulo}_habilitar`;
				const descricaoControlName = `${fasesExtra.titulo}_descricao`;
				const faseConfig = this.configuracaoFasesAi.find(
					(f) => f.codigometaextra === fasesExtra.codigo
				) || { habilitar: false, descricao: "" };

				// adiciona só se não existir
				if (!this.formGroupConfiguracaoIA.contains(habilitarControlName)) {
					this.formGroupConfiguracaoIA.addControl(
						habilitarControlName,
						new FormControl(null)
					);
				}
				if (!this.formGroupConfiguracaoIA.contains(descricaoControlName)) {
					this.formGroupConfiguracaoIA.addControl(
						descricaoControlName,
						new FormControl(null)
					);
				}

				this.formGroupConfiguracaoIA
					.get(habilitarControlName)
					.setValue(faseConfig.habilitar);
				this.formGroupConfiguracaoIA
					.get(descricaoControlName)
					.setValue(faseConfig.descricao);
			});
		} else {
			this.inputFasesExtras = [];
		}
	}

	loadCampanhas(config: any) {
		this.apiConfigIA
			.obterCampanhas(this.empresaSelecionada)
			.subscribe((response) => {
				if (response.result && response.result.length > 0) {
					this.fromControlCampanhas.setValue({
						content: [...response.result],
					});
				} else {
					this.fromControlCampanhas.setValue({
						content: [],
					});
				}
			});
	}

	loadConfiguracaoesGymbot(config: any) {
		this.obterTokenGymbotEmpresaExistente(config);
		if (
			(config.habilitarGymbot && config.tokenGymbot) ||
			this.formTokenGymbot.value !== null ||
			this.formTokenGymbot.value !== undefined
		) {
			this.apiConfigIA
				.obterDepartamentos(
					this.empresaSelecionada,
					this.configuracaoIA.tokenGymbot
				)
				.subscribe((res) => {
					if (res.content && res.content.length > 0) {
						this.departamentosGymbot.length = 0;
						const novosDepartamentos = res.content.map((departamento) => ({
							id: departamento.id,
							name: departamento.name,
						}));
						this.departamentosGymbot.push(...novosDepartamentos);
					} else {
						this.departamentosGymbot.length = 0;
					}
					this.departamentosGymbotVersion++;
					this.detectChanges.emit(true);
					this.reloaded.emit(true);

					setTimeout(() => {
						this.recreateFormGroupGymbot();
						this.refreshInputsConfiguracoesIa();
						this.detectChanges.emit(true);
						this.reloaded.emit(true);
					}, 200);
				});
			this.loadInstrucoesGymbotGrid();
		}
	}

	private loadDadosTelefoneConectado(config: any) {
		if (!config.idInstanciaZApi || !config.tokenZApi) {
			return;
		}

		this.apiConfigIA
			.obterWhatsappConectado(config.idInstanciaZApi, config.tokenZApi)
			.subscribe((res) => {
				if (res && res.phone) {
					this.formTelefoneConectado.setValue(res.phone);
				}
			});
	}

	private obterTokenGymbotEmpresaExistente(config: any) {
		if (!config.habilitarGymbot || !config.tokenGymbot) {
			this.apiConfigIA.consultarTokenGymbot(this.empresaSelecionada).subscribe({
				next: (res) => {
					if (res && res.token && config.tokenGymbot === null) {
						this.formTokenGymbot.setValue(res.token);
					}
				},
			});
		}
	}

	getInputs(): SubGrupoInputs[] {
		return [
			this.getInputRedeDeEmpresasIa(),
			this.getInputIntegracaoIa(),
			this.getInputAssistenteIa(),
			this.getInputProativos(),
			this.getInputFaseEMetaExtraIa(),
			this.getInputCampanhasIa(),
		];
	}

	loadConfiguracaoCrmEmpresa(empresa: any) {
		this._removeValidatorsHabilitarConversasAI(false);

		forkJoin(
			this.apiConfigIA.consultarConfiguracao(empresa.id),
			this.apiConfigIA.consultarConfiguracaoDeFases(empresa.id),
			this.apiConfigIA.consultarConfiguracaoDeFasesExtrasIA(empresa.id),
			this.discoveryService.consultarRede(this.sessionService.chave)
		).subscribe(
			([
				configuracaoIA,
				configuracaoFasesAi,
				configFasesExtraIA,
				gymNetwork,
			]) => {
				this.empresaSelecionada = empresa.id;
				this.configuracaoIA = configuracaoIA as any;
				this.configuracaoFasesAi =
					configuracaoFasesAi as ConfiguracaoCrmFaseIA[];
				this.fasesExtraIA = configFasesExtraIA as MaladiretaEntity[];

				this.updateFormControls(
					configuracaoIA,
					configuracaoFasesAi,
					configFasesExtraIA
				);
				const chavesBanco = gymNetwork
					? gymNetwork.map((banco) => {
							return {
								id: banco.chave,
								label: banco.chave,
							};
					  })
					: [];

				chavesBanco.forEach((chave) => {
					if (!this.chavesBanco.some((item) => item.id === chave.id)) {
						this.chavesBanco.push(chave);
					}
				});
				this.detectChanges.emit(true);
			}
		);
	}

	refreshInputsConfiguracoesIa() {
		this.formGroupConfiguracaoIA = new FormGroup({});
		this.getInputFaseEMetaExtraIa();
		if (this.configLoaded) {
			this.reloaded.emit(true);
		}
	}

	formularioConfiguracoesIa(configuracao: ConfiguracaoIA) {
		const config = configuracao;

		const horarioPadrao =
			config && config.horarioPadrao
				? config.horarioPadrao.substring(0, 5)
				: this.horarioDefault;

		this.horarioPadraoAnterior = horarioPadrao;
	}

	//casou falha ao carregar os campos verificar o motivo
	//deixei comentado para não quebrar o fluxo
	private _removeValidatorsHabilitarConversasAI(v) {
		if (!v) {
			this.formControlLoginPactoConversas.clearValidators();
			this.formControlSenhaPactoConversas.clearValidators();
			this.formEmailResponsavelConversasAI.clearValidators();
			this.formTelefoneResponsavelConversasAI.clearValidators();
			this.formControlInstanciaPactoConversas.clearValidators();
			this.formControlTokenPactoConversas.clearValidators();
		} else {
			this.validators();
		}
		this.formControlLoginPactoConversas.updateValueAndValidity();
		this.formControlSenhaPactoConversas.updateValueAndValidity();
		this.formEmailResponsavelConversasAI.updateValueAndValidity();
		this.formTelefoneResponsavelConversasAI.updateValueAndValidity();
		this.formControlInstanciaPactoConversas.updateValueAndValidity();
		this.formControlTokenPactoConversas.updateValueAndValidity();
	}

	selecionarFaseConversaModal() {
		const fases = this.fases.map((fase) => {
			return { label: fase.descricao, id: fase.name };
		});
		if (this.fasesExtraIA && this.fasesExtraIA.length !== 0) {
			this.fasesExtraIA.forEach((faseExtra) => {
				const faseExtraConfig = this.configuracaoFasesAi.find(
					(f) => f.codigometaextra === faseExtra.codigo
				);
				if (
					faseExtraConfig &&
					faseExtraConfig.habilitar &&
					!fases.some((f) => f.id === faseExtra.titulo)
				) {
					fases.push({
						label: faseExtra.titulo,
						id: faseExtra.titulo,
					});
				}
			});
		}

		setTimeout(() => {
			const mensagemError = "Erro ao iniciar conversa fase: ";
			const sucesso = "Conversas Iniciadas com sucesso!";
			const dialog = this.dialogService.open(
				"",
				FaseDialogComponent,
				PactoModalSize.LARGE
			);
			dialog.componentInstance.list = fases;
			dialog.componentInstance.title =
				"Selecione as fases para iniciar a conversa";
			dialog.componentInstance.actionLabel = "Iniciar Conversa";
			dialog.componentInstance.formControl = this.formControlFaseModal;
			dialog.result.then((instance) => {
				this.apiConfigIA
					.iniciarConversaPorFase(
						this.sessionService.chave,
						this.empresaSelecionada,
						[instance.value.id]
					)
					.subscribe({
						next: (result) => {
							this.notificationService.success(sucesso);
						},
						error: (error) => {
							this.notificationService.error(mensagemError + error.message);
						},
					});
			});
		});
	}

	selectCampoHorarios() {
		const horarios = [];
		for (let hora = 0; hora < 24; hora++) {
			const horaFormatada = hora.toString().padStart(2, "0");
			const horario = `${horaFormatada}:00`;
			horarios.push({
				id: horario,
				label: horario,
			});
		}
		return horarios;
	}

	hideButtonSelecionarFaseConversa() {
		const username = this.sessionService.loggedUser.username
			.toLowerCase()
			.trim();
		return !(username === "pactobr" || username === "admin");
	}

	hideUserDifferentPactoBr() {
		const username = this.sessionService.loggedUser.username
			.toLowerCase()
			.trim();
		return !(username === "pactobr");
	}

	hideButtonCopiarWhatsappLink() {
		return !this.linkWhatsApp || this.linkWhatsApp === "";
	}

	validators() {
		[
			this.formControlTokenPactoConversas,
			this.formControlInstanciaPactoConversas,
		].forEach((formControl) => {
			formControl.setValidators([Validators.min(0), tokenInstanceValidator]);
		});
	}

	getInputAssistenteIa(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.IA_ASSISTENTE,
			inputs: [
				{
					name: "personalidade",
					formControl: this.formControlPersonalidade,
					description: `A personalidade da IA é um fator importante para a interação com os usuários e a eficácia das respostas
														e ações realizadas pelo sistema. A personalidade da IA pode ser configurada para refletir a identidade
														e a comunicação desejadas para a interação com os usuários.`,
					title: "Personalidade IA",
					type: ConfigItemType.TEXT_AREA,
					id: "pesonalidade",
					size: "large",
					customClass: "p-flex-wrap-important",
					rows: 6,
				},
				{
					name: "informacoesAdicionaisAcademia",
					formControl: this.formControlInformacoesAdicionaisAcademia,
					description: `Este campo permite a inserção de informações contextuais relevantes que serão utilizadas para enriquecer
														as interações no recurso de chat com IA. As informações podem incluir detalhes específicos sobre as atividades,
														horário de funcionamento, perfil da academia, ou qualquer outra informação que ajude a personalizar e melhorar
														 a precisão das respostas da IA no contexto da academia.`,
					title: "Informações adicionais sobre a academia",
					type: ConfigItemType.TEXT_AREA,
					size: "large",
					customClass: "p-flex-wrap-important",
					id: "informacoesAdicionaisAcademia",
					rows: 6,
				},
				{
					name: "inputFile",
					formControl: this.formControlInputFile,
					description: `Envie um PDF com informações do seu negócio para que a IA utilize este material como referência.`,
					title: "Informações Complementares (PDF)",
					formatos: ".pdf",
					formatosValidos: /(pdf)$/i,
					type: ConfigItemType.INPUTFILE,
					onClick: () => {
						this.formControlInputFile.markAsTouched();
					},
				},
			],
		};
	}

	getInputProativos(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.IA_PROATIVOS,
			inputs: [
				{
					type: ConfigItemType.INPUT_GRID,
					title: "Contatos Proativos",
					description: `Realizar contatos proativos para lembrar os agendamentos de aulas melhorar o engajamento e reduzir os no-shows nas aulas,
						 realizando contatos proativos que lembrem os alunos dos seus agendamentos.
						 O fluxo de comunicação deverá variar conforme o tempo de antecedência do agendamento e incluir orientações.`,
					textarea: {
						name: "descricaoContatoProativo",
						formControl: this.formControlDescricaoContatoProativo,
						title: "Descrição Contato Proativo:",
						type: ConfigItemType.TEXT_AREA,
						rows: 4,
						size: "large",
						id: "descricaoContatoProativo",
					},
					btnfuncion: {
						typeDescription: "Enviar proativos",
						type: ConfigItemType.BUTTON,
						typeButton: "PRIMARY",
						onClick: () => {
							const data = {
								descricaoProativo:
									this.formControlDescricaoContatoProativo.value,
								codigoEmpresa: this.empresaSelecionada,
							};
							this.apiConfigIA.enviarConfiguracaoNotificacao(data).subscribe({
								next: (res) => {
									this.notificationService.success(
										"Proativo enviado com sucesso!"
									);
									this.obterNotificacoesProativo();
								},
								error: (error) => {
									this.notificationService.error(
										"Erro ao enviar proativo: " + error.message
									);
									return throwError(error);
								},
							});
						},
					},
				},
				{
					title: "Dia evento : ",
					type: ConfigItemType.GRID,
					codigo: false,
					description: `Notificações para o dia do evento`,
					dataGridConfig: this.getGridNotificacoes(this.formControlDiaDoEvento),
					hide: () =>
						!this.formControlDiaDoEvento.value ||
						this.formControlDiaDoEvento.value.content.length === 0,
				},
				{
					title: "Dias Itermediarios : ",
					type: ConfigItemType.GRID,
					codigo: false,
					description: `Notificações para dias intermediários`,
					dataGridConfig: this.getGridNotificacoes(
						this.formControlDiasIntermediarios
					),
					hide: () =>
						!this.formControlDiasIntermediarios.value ||
						this.formControlDiasIntermediarios.value.content.length === 0,
				},
				{
					title: "Mesmo dia : ",
					type: ConfigItemType.GRID,
					codigo: false,
					description: `Notificações para o mesmo dia`,
					dataGridConfig: this.getGridNotificacoes(this.formControlMesmoDia),
					hide: () =>
						!this.formControlMesmoDia.value ||
						this.formControlMesmoDia.value.content.length === 0,
				},
			],
		};
	}

	obterNotificacoesProativo() {
		this.formControlMesmoDia.setValue({ content: [] });
		this.formControlDiasIntermediarios.setValue({ content: [] });
		this.formControlDiaDoEvento.setValue({ content: [] });

		this.apiConfigIA
			.obterNotificacoesProativo(this.empresaSelecionada)
			.subscribe(
				(res) => {
					const contexto = res.result[0].contexto;
					if (!contexto) {
						this.detectChanges.emit(true);
						return;
					}
					const campos = [
						{ key: "same_day", control: this.formControlMesmoDia },
						{
							key: "intermediate_days",
							control: this.formControlDiasIntermediarios,
						},
						{ key: "event_day", control: this.formControlDiaDoEvento },
					];

					campos.forEach(({ key, control }) => {
						const data = contexto[key];
						if (data) {
							const todasNotificacoes = this.mergeNotifications(
								data.notifications_before,
								data.notifications_at,
								data.notifications_after
							);

							const content = todasNotificacoes.map((item) => ({
								mensagem: item.message_instructions,
								hora: item.time,
								horario: `${item.time} ${item.unit || ""}`,
								tipo: item.type,
							}));

							control.setValue({ content });
						}
					});
					this.detectChanges.emit(true);
				},
				(error) => {
					this.notificationService.error(
						"Erro ao carregar notificações proativas: " + error.message
					);
				}
			);
	}

	iniciarVerificacaoDeAtualizaDaNotificacoesLead() {
		this.carregandoRegua = true;
		this.detectChanges.emit(true);
		this.esconderPactoRelatorioRenderer();
		this.intervalo = setInterval(() => {
			this.verificarAtualizacao();
		}, 3000);
	}

	verificarAtualizacao() {
		this.apiConfigIA.obterNotificacoesLead(this.empresaSelecionada).subscribe(
			(res) => {
				this.loaderService.stopForce();
				const contexto = res.result[0].contexto;
				const idAtual = contexto.id;
				const status = contexto.status;
				if (status == null && idAtual === 1) {
					// this.ultimoId = idAtual;
					clearInterval(this.intervalo);
					this.atualizarTabela(contexto);
				}
			},
			(error) => {
				this.notificationService.error(
					"Erro ao verificar notificações proativas: " + error.message
				);
			}
		);
		this.loaderService.stopForce();
	}

	atualizarTabela(contexto: any) {
		const content = [];
		contexto.schema.forEach((item) => {
			content.push({
				mensagem: item["instruction"],
				dia: item["days_after"],
			});
		});
		this.formControlTabelaDeReguaDeAtendimento.setValue({ content });
		this.carregandoRegua = false;
		this.esconderPactoRelatorioRenderer();
		this.detectChanges.emit(true);
	}

	esconderPactoRelatorioRenderer() {
		this.elemento = document.querySelector("pacto-relatorio-renderer");

		if (this.carregandoRegua) {
			// Esconde o componente original
			if (this.elemento) {
				this.elemento.style.display = "none";
			}

			if (!this.spinner) {
				this.spinner = document.createElement("div");
				this.spinner.style.display = "flex";
				this.spinner.style.flexDirection = "column";
				this.spinner.style.alignItems = "center";
				this.spinner.style.justifyContent = "center";
				this.spinner.style.height = "170px";
				this.spinner.style.width = "100%";
				this.spinner.innerHTML = `
          <img src= "${this.treinoFrontURL}/pt/assets/images/gif/loading-pacto.gif" alt="Carregando..." style="width: 64px; height: 64px; margin-bottom: 12px;" />
          <span style="font-size: 14px; color: #555;">Carregando régua...</span>
        `;

				this.elemento.parentElement.insertBefore(this.spinner, this.elemento);
			}

			this.spinner.style.display = "flex";
		} else {
			// Mostra o componente original
			if (this.elemento) {
				this.elemento.style.display = "block";
			}

			// Esconde o spinner
			if (this.spinner) {
				this.spinner.style.display = "none";
			}
		}
	}

	obterNotificacoesLead() {
		const content = [];
		this.apiConfigIA.obterNotificacoesLead(this.empresaSelecionada).subscribe(
			(res) => {
				const contexto = res.result[0].contexto;

				if (contexto.status === "processing") {
					this.iniciarVerificacaoDeAtualizaDaNotificacoesLead();
					return;
				}

				contexto.schema.forEach((item) => {
					content.push({
						mensagem: item["instruction"],
						dia: item["days_after"],
					});
				});
				this.formControlTabelaDeReguaDeAtendimento.setValue({ content });
				this.carregandoRegua = false;
				this.esconderPactoRelatorioRenderer();
				this.detectChanges.emit(true);
			},
			(error) => {
				this.notificationService.error(
					"Erro ao carregar notificações proativas: " + error.message
				);
			}
		);
	}

	buscarNotificacoesLead() {
		const content = [];
		this.carregandoRegua = true;
		this.esconderPactoRelatorioRenderer();
		this.apiConfigIA.obterNotificacoesLead(this.empresaSelecionada).subscribe(
			(res) => {
				const contexto = res.result[0].contexto;
				const id = res.result[0].contexto.id;

				contexto.schema.forEach((item) => {
					content.push({
						mensagem: item["instruction"],
						dia: item["days_after"],
					});
				});
				this.formControlTabelaDeReguaDeAtendimento.setValue({ content });
				this.carregandoRegua = false;
				this.esconderPactoRelatorioRenderer();
				this.detectChanges.emit(true);
			},
			(error) => {
				this.notificationService.error(
					"Erro ao carregar notificações proativas: " + error.message
				);
			}
		);
	}

	mergeNotifications(
		notifications_before: NotificationBeforeAfter[] | null,
		notifications_at: NotificationAt[] | null,
		notifications_after: NotificationBeforeAfter[] | null
	): TypedNotification[] {
		const merged: TypedNotification[] = [];

		if (notifications_before) {
			for (const n of notifications_before) {
				merged.push({
					type: "antes",
					message_instructions: n.message_instructions,
					time: n.time,
					unit: n.unit,
				});
			}
		}

		if (notifications_at) {
			for (const n of notifications_at) {
				merged.push({
					type: "atual",
					message_instructions: n.message_instructions,
					time: n.time,
				});
			}
		}

		if (notifications_after) {
			for (const n of notifications_after) {
				merged.push({
					type: "depois",
					message_instructions: n.message_instructions,
					time: n.time,
					unit: n.unit,
				});
			}
		}

		return merged;
	}

	getGridNotificacoes(fromControl: FormControl): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: fromControl,
			columns: this.getGridColumns(),
		});
	}

	getGridNotificacoesLead(fromControl: FormControl): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: fromControl,
			columns: this.getGridColumnsLead(),
		});
	}

	getGridColumns(): PactoDataGridColumnConfigDto[] {
		return [
			{
				nome: "tipo",
				titulo: "Tipo",
				visible: true,
				ordenavel: true,
			},
			{
				nome: "mensagem",
				titulo: "Mensagem",
				visible: true,
				ordenavel: false,
			},
			{
				nome: "horario",
				titulo: "Horario",
				visible: true,
				ordenavel: false,
			},
		];
	}

	getGridColumnsLead(): PactoDataGridColumnConfigDto[] {
		return [
			{
				nome: "dia",
				titulo: "Dias",
				visible: true,
				ordenavel: false,
			},
			{
				nome: "mensagem",
				titulo: "Mensagem",
				visible: true,
				ordenavel: false,
			},
		];
	}

	getInputIntegracaoIa(): SubGrupoInputs {
		this.formularioConfiguracoesIa(this.configuracaoIA);
		return {
			subgrupo: ConfigModuloSubGroup.IA_INTEGRACAO,
			inputs: [
				{
					name: "habilitarconfigia",
					formControl: this.formHabilitarPactoConversas,
					description: `Ao habilitar o Conversas AI, é possível integrar o sistema Pacto com recursos de chat e WhatsApp utilizando inteligência artificial.`,
					type: ConfigItemType.CHECKBOX,
					title: "Habilitar Conversas AI",
					typeDescription: "Habilitar Conversas AI",
					children: [
						{
							name: "loginPactoConversas",
							formControl: this.formControlLoginPactoConversas,
							description: `Para acessar o recurso de chat com IA, é necessário informar o login de integração do sistema Pacto.`,
							title: "Login Conversas AI",
							type: ConfigItemType.TEXT,
							hide: () => this.hideUserDifferentPactoBr(),
						},
						{
							name: "senhaPactoConversas",
							formControl: this.formControlSenhaPactoConversas,
							description: `Senha de integração sistema Pacto e o recurso de chat com IA.`,
							title: "Senha Conversas AI",
							type: ConfigItemType.PASSWORD,
							disabled: !this.hideButtonSelecionarFaseConversa(),
							hide: () => this.hideUserDifferentPactoBr(),
						},
						{
							name: "emailResponsavelConversasAI",
							formControl: this.formEmailResponsavelConversasAI,
							description: `Email de integração sistema Pacto e o recurso de chat com IA.`,
							title: "Email Conversas AI",
							validators: [Validators.email],
							type: ConfigItemType.TEXT,
						},
						{
							name: "telefoneResponsavelConversasAI",
							formControl: this.formTelefoneResponsavelConversasAI,
							description: `Telefone de integração sistema Pacto e o recurso de chat com IA.`,
							title: "Telefone Conversas AI",
							type: ConfigItemType.TEXT,
							placeholder: "(99) 99999-9999",
							textMask: [
								"(",
								...Array(2).fill(/\d/),
								")",
								9,
								" ",
								...Array(4).fill(/\d/),
								"-",
								...Array(4).fill(/\d/),
							],
						},
						{
							name: "zwid",
							formControl: this.formControlInstanciaPactoConversas,
							description: `id de instancia da integração z-api`,
							title: "Instancia ID",
							type: ConfigItemType.TEXT,
							maxlength: 32,
							hide: () => this.hideUserDifferentPactoBr(),
						},
						{
							name: "token",
							formControl: this.formControlTokenPactoConversas,
							description: `Token de integração z-api`,
							title: "Token",
							type: ConfigItemType.TEXT,
							maxlength: 32,
							hide: () => this.hideUserDifferentPactoBr(),
						},
					],
				},
				{
					name: "conectarwhatsApp",
					formControl: this.formControlWhatsAppBusiness,
					description: `Conecte seu número de WhatsApp e permitir que a IA interaja com seus clientes através desse canal`,
					title: "Conectar ao WhatsApp",
					type: ConfigItemType.ACTIONS,
					maxlength: 32,
					link: this.linkWhatsApp,
					btnfuncion: {
						title: "Conectar ao WhatsApp",
						typeDescription: "Conectar ao WhatsApp",
						icon: "pct-telemarketing",
						description:
							"Conecte seu número de WhatsApp e permitir que a IA interaja com seus clientes através desse canal",
						type: ConfigItemType.BUTTON,
						typeButton: "PRIMARY",
						onClick: () => {
							const dialog: PactoModalRef = this.dialogService.open(
								"Conectar ao WhatsApp",
								QrCodeDialogComponent,
								PactoModalSize.MEDIUM,
								"modal-acesso-qrcode"
							);
							dialog.componentInstance.codigoEmpresa = this.empresaSelecionada;
							dialog.componentInstance.token = this
								.formControlTokenPactoConversas.value
								? this.formControlTokenPactoConversas.value
								: "";

							dialog.componentInstance.idInstancia = this
								.formControlInstanciaPactoConversas.value
								? this.formControlInstanciaPactoConversas.value
								: "";

							dialog.result.then((result) => {
								this.formControlTokenPactoConversas.setValue(result.token);
								this.formControlInstanciaPactoConversas.setValue(
									result.idInstancia
								);
								this.formControlWhatsAppBusiness.setValue(
									result.whatsappBusiness
								);
								this.detectChanges.emit(true);
							});
						},
					},
					btnfuncion2: {
						title: "Copiar link do WhatsApp",
						typeDescription: "Copiar link do WhatsApp",
						icon: "pct pct-copy",
						description:
							"Conecte seu número de WhatsApp e permitir que a IA interaja com seus clientes através desse canal",
						type: ConfigItemType.BUTTON,
						onClick: () => {
							if (!this.formTelefoneConectado.value) {
								this.notificationService.warning(
									"Necessário conectar ao whatsapp!"
								);
								return;
							}
							if (this.formTelefoneConectado.value !== null) {
								this.copyMessage(this.linkWhatsApp);
							}
						},
						hide: () => this.hideButtonCopiarWhatsappLink(),
					},
				},
				{
					name: "habilitarconfiggymbot",
					title: "Habilitar Configuração Gymbot",
					description:
						"Habilitar Configuração Gymbot é uma funcionalidade que permite a integração do sistema Pacto com o recurso de chat com IA.",
					type: ConfigItemType.CHECKBOX,
					formControl: this.formHabilitarGymbot,
					children: [
						{
							type: ConfigItemType.INPUT_GRID,
							title: "Configuração Gymbot",
							inputText: {
								name: "tokenGymbot",
								formControl: this.formTokenGymbot,
								title: "Token",
								type: ConfigItemType.TEXT,
								placeholder:
									"00000000000000000000000000000000000000000000000000",
								maxlength: 64,
								validators: [
									Validators.required,
									Validators.pattern(/^pn_[A-Za-z0-9]{40,}$/),
								],
							},
							sincronizar: {
								type: ConfigItemType.BUTTON,
								title: "Atualizar Token",
								typeDescription: "",
								icon: "pct pct-refresh-ccw cor-azulim05",
								typeButton: "OUTILINE",
								onClick: () => {
									this.departamentosGymbot = [];
									this.formGroupConfiguracaoGymbot
										.get("formControlDepartamento")
										.disable();
									this.formGroupConfiguracaoGymbot
										.get("formControlInstrucao")
										.disable();
									const token = this.formTokenGymbot.value;
									if (!token || token.trim() === "") {
										this.notificationService.warning(
											"Por favor, informe o token Gymbot."
										);
										return;
									}
									if (!/^pn_[A-Za-z0-9]{40,}$/.test(token)) {
										this.notificationService.error(
											"Token inválido. Obtenha um token valido para o gymbot."
										);
										this.formTokenGymbot.setErrors({ invalid: true });
										return;
									}
									this.apiConfigIA
										.atualizarToken(this.empresaSelecionada, token)
										.subscribe({
											next: (res) => {
												this.notificationService.info(
													"Atualizando departamentos Gymbot, aguarde..."
												);
												this.loaderService.initForce();
												this.loaderService.show();

												of(null)
													.pipe(delay(3000))
													.subscribe(() => {
														this.formGroupConfiguracaoGymbot.reset();
														this.obterDepartamentosGymbot();
														this.loadInstrucoesGymbotGrid();
														this.formGroupConfiguracaoGymbot
															.get("formControlDepartamento")
															.enable();
														this.formGroupConfiguracaoGymbot
															.get("formControlInstrucao")
															.enable();
														this.reloaded.emit(true);
														this.loaderService.hide();
														this.loaderService.stopForce();
													});
											},
											error: (error) => {
												this.notificationService.error(
													"Erro ao atualizar token: " + error.message
												);
												this.formGroupConfiguracaoGymbot
													.get("formControlToken")
													.setErrors({ invalid: true });
											},
										});
								},
							},
							select: {
								name: "departamento",
								formControl: this.formGroupConfiguracaoGymbot.get(
									"formControlDepartamento"
								),
								title: "Departamento",
								type: ConfigItemType.SELECTFILTER,
								options: this.getDepartamentosGymbotOptions(),
								labelKey: "name",
								idKey: "id",
								size: "large",
								placeholder: "Selecione o departamento",
							},
							textarea: {
								name: "instrucaoGymbot",
								formControl: this.formGroupConfiguracaoGymbot.get(
									"formControlInstrucao"
								),
								title: "Instrução IA",
								type: ConfigItemType.TEXT_AREA,
								rows: 6,
								size: "large",
								id: "instrucaoGymbot",
							},
							addBtn: {
								typeDescription: "Salvar Departamento",
								icon: "pct pct-crosshair",
								type: ConfigItemType.BUTTON,
								typeButton: "PRIMARY",
								onClick: () => {
									if (
										this.formGroupConfiguracaoGymbot.get(
											"formControlDepartamento"
										).value == null
									) {
										this.notificationService.warning(
											"Selecione um departamento"
										);
										return;
									}
									if (
										this.formGroupConfiguracaoGymbot.get("formControlInstrucao")
											.value == null ||
										this.formGroupConfiguracaoGymbot.get("formControlInstrucao")
											.value === ""
									) {
										this.notificationService.warning(
											"Adicione uma instrução para o departamento selecionado."
										);
										return;
									}
									const dto = {
										tokenGymbot: this.formTokenGymbot.value,
										codigoEmpresa: this.empresaSelecionada,
										codigoConfiguracao: this.configuracaoIA.codigo,
										departamentos: [
											{
												id: this.formGroupConfiguracaoGymbot.get(
													"formControlDepartamento"
												).value.id,
												name: this.formGroupConfiguracaoGymbot.get(
													"formControlDepartamento"
												).value.name,
												descricao: this.formGroupConfiguracaoGymbot.get(
													"formControlInstrucao"
												).value,
											},
										],
										habilitarGymbot: this.formHabilitarGymbot.value,
									};
									if (
										this.codigoConfiguracao === null ||
										this.codigoConfiguracao === undefined
									) {
										this.notificationService.warning(
											"Não foi possível salvar a configuração Gymbot, Salve uma configuração da empresa."
										);
										return;
									}
									this.apiConfigIA
										.enviarConfiguracaoGymbot(dto)
										.pipe(
											switchMap(() => timer(1000)),
											tap(() => {
												this.notificationService.success(
													"Departamento salvo com sucesso."
												);
												this.formGroupConfiguracaoGymbot
													.get("formControlDepartamento")
													.reset();
												this.formGroupConfiguracaoGymbot
													.get("formControlInstrucao")
													.reset();
											}),
											switchMap(() =>
												this.apiConfigIA.obterInstrucoesGymbot(
													this.empresaSelecionada
												)
											),
											tap((response) => {
												this.formControlGymbotData.setValue({
													content: response.length > 0 ? response : [],
												});
											}),
											catchError((error) => {
												this.notificationService.error(
													"Erro ao carregar instruções: " + error.message
												);
												return of([]);
											})
										)
										.subscribe(() => {
											this.loadInstrucoesGymbotGrid();
										});
								},
							},
						},
						{
							title: "Lista de departamentos Gymbot",
							type: ConfigItemType.GRID,
							codigo: false,
							dataGridConfig: this.getGridGymbot(this.formControlGymbotData),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
							isAddRowAvailable: false,
						},
					],
				},
				{
					title: "Escolher Fase",
					description: "Selecione as fases para iniciar a conversa",
					typeDescription: "Escolher Fase",
					type: ConfigItemType.BUTTON,
					onClick: () => this.selecionarFaseConversaModal(),
					hide: () => this.hideButtonSelecionarFaseConversa(),
				},
				{
					title: "Processar Metas",
					description: "Clique para processar as metas",
					typeDescription: "Processar Metas",
					type: ConfigItemType.BUTTON,
					onClick: () => {
						this.apiConfigIA.processarMetas().subscribe((res) => {
							this.notificationService.success(
								"Metas processadas com sucesso!"
							);
						}),
							(erro) => {
								this.notificationService.error(
									"Erro ao processar metas: " + erro.message
								);
							};
					},
					hide: () => this.hideUserDifferentPactoBr(),
				},
				{
					name: "horarioPadrao",
					formControl: this.formControlHorarioPadrao,
					description: "Horário padrão para o conversas AI",
					title: "Horário",
					type: ConfigItemType.SELECT,
					options: this.selectCampoHorarios(),
				},
				{
					name: "desabilitarAgendamentoAulaExperimental",
					formControl: this.formControlDesabilitarAgendamentoAulaExperimental,
					description: `Desabilitar agendamento de aulas experimentais para o recurso de chat com IA.`,
					title: "Desabilitar agendamento de aulas experimentais",
					type: ConfigItemType.CHECKBOX,
					typeDescription: "Desabilitar agendamento de aulas experimentais",
				},
			],
		};
	}

	getGridGymbot(fromControl: FormControl): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			endpointUrl: `${
				this.discoveryService.getUrlMap().contatoMsUrl
			}/v1/configuracao/gymbot/departamentos-instrucao?codigoEmpresa=${
				this.empresaSelecionada || 0
			}`,
			formGroup: new FormGroup({
				departamento: new FormControl(),
				descricao: new FormControl(),
			}),
			dataFormControl: fromControl,
			columns: [
				{
					nome: "id_departamento",
					titulo: "ID",
					visible: false,
					ordenavel: true,
					editable: false,
				},
				{
					nome: "departamento",
					titulo: "Departamento",
					visible: true,
					ordenavel: true,
					editable: false,
				},
				{
					nome: "descricao",
					titulo: "Instrução",
					visible: true,
					ordenavel: false,
					editable: false,
				},
			],
			actions: [
				{
					nome: "Editar",
					iconClass: "pct pct-edit cor-action-default-able04",
					actionFn: (data) => {
						const departamento = data.row;

						this.formGroupConfiguracaoGymbot
							.get("formControlDepartamento")
							.setValue({ id: departamento.id_departamento });
						this.formGroupConfiguracaoGymbot
							.get("formControlInstrucao")
							.setValue(departamento.descricao);

						return of(true);
					},
				},
				{
					nome: "Excluir",
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					actionFn: (data, rowIndex: number) => {
						const id_departamento = data.row.id_departamento;
						this.apiConfigIA
							.removerInstrucoesGymbot(this.empresaSelecionada, [
								id_departamento,
							])
							.pipe(
								switchMap(() => timer(1000)),
								tap(() => {
									this.notificationService.success(
										"Departamento exluido com sucesso."
									);
								}),
								switchMap(async () => this.loadInstrucoesGymbotGrid()),
								catchError((error) => {
									this.notificationService.error(
										"Erro ao remover Departamento"
									);
									return of([]);
								})
							)
							.subscribe();
						return of(true);
					},
				},
			],
		});
	}

	getDepartamentosGymbotOptions(): any[] {
		return this.departamentosGymbot.map((dep) => ({
			...dep,
			_version: this.departamentosGymbotVersion,
		}));
	}

	recreateFormGroupGymbot(): void {
		const currentValues = this.formGroupConfiguracaoGymbot.value;
		this.formGroupConfiguracaoGymbot = new FormGroup({
			formControlToken: new FormControl(currentValues.formControlToken || ""),
			formControlDepartamento: new FormControl(
				currentValues.formControlDepartamento || ""
			),
			formControlInstrucao: new FormControl(
				currentValues.formControlInstrucao || ""
			),
		});
	}

	obterDepartamentosGymbot(): void {
		this.apiConfigIA
			.obterDepartamentos(this.empresaSelecionada, this.formTokenGymbot.value)
			.subscribe({
				next: (res: { content: { id: string; name: string }[] }) => {
					if (res.content && res.content.length > 0) {
						this.departamentosGymbot.length = 0;
						const novosDepartamentos = res.content.map((departamento) => ({
							id: departamento.id,
							name: departamento.name,
						}));
						this.departamentosGymbot.push(...novosDepartamentos);
					} else {
						this.departamentosGymbot.length = 0;
					}
					this.departamentosGymbotVersion++;
					this.detectChanges.emit(true);
					this.reloaded.emit(true);

					setTimeout(() => {
						this.recreateFormGroupGymbot();
						this.refreshInputsConfiguracoesIa();
						this.detectChanges.emit(true);
						this.reloaded.emit(true);
					}, 200);
				},
				error: (error: any) => {
					this.notificationService.error(
						"Erro ao validar o token Gymbot: " + error.message
					);
				},
			});
	}

	getInputFaseEMetaExtraIa(): SubGrupoInputs {
		const inputs = this.fases.map((fase) => {
			const habilitarControlName = fase.name + "_habilitar";
			const descricaoControlName = fase.name + "_descricao";
			const mensagensExtrasControlName = fase.name + "_mensagens_extras";

			const faseConfig = this.configuracaoFasesAi.find(
				(f) => f.fase === fase.name
			) || { habilitar: false, descricao: "" };

			if (!this.formGroupConfiguracaoIA.contains(habilitarControlName)) {
				this.formGroupConfiguracaoIA.addControl(
					habilitarControlName,
					new FormControl(faseConfig.habilitar)
				);
			}
			if (!this.formGroupConfiguracaoIA.contains(descricaoControlName)) {
				this.formGroupConfiguracaoIA.addControl(
					descricaoControlName,
					new FormControl(
						faseConfig.descricao || ModeloPrompts[fase.name] || ""
					)
				);
			}
			if (!this.formGroupConfiguracaoIA.contains(mensagensExtrasControlName)) {
				this.formGroupConfiguracaoIA.addControl(
					mensagensExtrasControlName,
					new FormControl()
				);
			}

			const mensagensExtras = fase.name === "LEADS_HOJE";

			if (!mensagensExtras) {
				return {
					name: habilitarControlName,
					formControl: this.formGroupConfiguracaoIA.get(habilitarControlName),
					description: fase.objetivo,
					title: fase.descricao,
					type: ConfigItemType.CHECKBOX,
					children: [
						{
							name: descricaoControlName,
							formControl:
								this.formGroupConfiguracaoIA.get(descricaoControlName),
							title: "Instrução : ",
							type: ConfigItemType.TEXT_AREA,
							size: "large",
							customClass: "p-flex-wrap-important",
							rows: 6,
						},
					],
				};
			} else {
				return {
					name: habilitarControlName,
					formControl: this.formGroupConfiguracaoIA.get(habilitarControlName),
					description: fase.objetivo,
					title: fase.descricao,
					type: ConfigItemType.CHECKBOX,
					children: [
						{
							name: descricaoControlName,
							formControl:
								this.formGroupConfiguracaoIA.get(descricaoControlName),
							title: "Instrução : ",
							type: ConfigItemType.TEXT_AREA,
							size: "large",
							customClass: "p-flex-wrap-important",
							rows: 6,
						},
						{
							type: ConfigItemType.INPUT_GRID,
							title: "Régua de Atendimento",
							description: `Realizar uma régua de atendimento estruturada para garantir uma comunicação contínua e eficiente com os leads, realizando contatos e tentando convertê-los.`,
							textarea: {
								name: "descricaoContatoProativo2",
								id: "descricaoContatoProativo2",
								placeholder:
									"Ex.: \n" +
									"1 Dia - Convidar o aluno para uma aula experimental na academia; \n" +
									"7 Dias - Oferecer uma aula experimental e uma promoção para ir à academia hoje; \n" +
									"15 Dias - ...\n",
								formControl: this.formGroupConfiguracaoIA.get(
									mensagensExtrasControlName
								),
								title: "Instrução da Régua de Atendimento:",
								type: ConfigItemType.TEXT_AREA,
								rows: 4,
								size: "large",
							},
							btnfuncion: {
								typeDescription: "Gerar Régua de Atendimento",
								type: ConfigItemType.BUTTON,
								disabled: () => {
									return (
										!this.formGroupConfiguracaoIA.get(
											mensagensExtrasControlName
										).value || this.carregandoRegua
									);
								},
								typeButton: "PRIMARY",
								onClick: () => {
									const data = {
										descricaoReguaAtendimento: this.formGroupConfiguracaoIA.get(
											mensagensExtrasControlName
										).value,
										codigoEmpresa: this.empresaSelecionada,
									};
									this.apiConfigIA
										.enviarConfiguracaoNotificacaoLeads(data)
										.subscribe(
											(res) => {
												this.notificationService.success(
													"Régua de Atendimento sendo gerada!"
												);
												this.iniciarVerificacaoDeAtualizaDaNotificacoesLead();
											},
											catchError((error) => {
												this.notificationService.error(
													"Erro ao enviar proativo: " + error.message
												);
												return throwError(error);
											})
										);
								},
							},
						},
						{
							title: "Régua de Atendimento Gerada por IA:",
							type: ConfigItemType.GRID,
							codigo: false,
							description: `Notificações para dias intermediários`,
							dataGridConfig: this.getGridNotificacoesLead(
								this.formControlTabelaDeReguaDeAtendimento
							),
						},
					],
				};
			}
		});

		if (this.fasesExtraIA && this.fasesExtraIA.length > 0) {
			this.inputFasesExtras = this.fasesExtraIA.map((fasesExtra) => {
				const habilitarControlName = fasesExtra.titulo + "_habilitar";
				const descricaoControlName = fasesExtra.titulo + "_descricao";

				const faseConfig = this.configuracaoFasesAi.find(
					(f) => f.codigometaextra && f.codigometaextra === fasesExtra.codigo
				) || { habilitar: false, descricao: "" };

				if (!this.formGroupConfiguracaoIA.contains(habilitarControlName)) {
					this.formGroupConfiguracaoIA.addControl(
						habilitarControlName,
						new FormControl(faseConfig.habilitar)
					);
				}
				if (!this.formGroupConfiguracaoIA.contains(descricaoControlName)) {
					this.formGroupConfiguracaoIA.addControl(
						descricaoControlName,
						new FormControl(faseConfig.descricao || "")
					);
				}

				return {
					name: habilitarControlName,
					formControl: this.formGroupConfiguracaoIA.get(habilitarControlName),
					description: "Meta extra definida manualmente na plataforma",
					title: fasesExtra.titulo,
					type: ConfigItemType.CHECKBOX,
					children: [
						{
							name: descricaoControlName,
							formControl:
								this.formGroupConfiguracaoIA.get(descricaoControlName),
							title: "Instrução : ",
							type: ConfigItemType.TEXT_AREA,
							size: "large",
							rows: 6,
						},
					],
				};
			});
		}

		return {
			subgrupo: ConfigModuloSubGroup.IA_FASE_E_META_EXTRA,
			inputs: [...inputs, ...(this.inputFasesExtras || [])],
		};
	}

	private atualizarFases(configuracaoAtualizada: any[]) {
		this.fases.map((fase) => {
			const habilitarControlName = fase.name + "_habilitar";
			const descricaoControlName = fase.name + "_descricao";
			const mensagensExtrasControlName = fase.name + "_mensagens_extras";

			const habilitar =
				this.formGroupConfiguracaoIA.get(habilitarControlName).value;
			const descricao =
				this.formGroupConfiguracaoIA.get(descricaoControlName).value;
			const mensagensextras = this.formGroupConfiguracaoIA.get(
				mensagensExtrasControlName
			).value;

			configuracaoAtualizada.push({
				fase: fase.name,
				nomemetaextra: null,
				codigometaextra: null,
				habilitar,
				descricao: habilitar ? descricao : "",
				mensagensextras: habilitar ? mensagensextras : "",
			});
		});
	}

	setConfigRedeSelecionada(configRedeSelecionada: any) {
		this.configRedeSelecionada = configRedeSelecionada;
	}

	getInputRedeDeEmpresasIa(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.IA_REDE_DE_EMPRESAS,
			inputs: [
				{
					name: "redeEmpresa",
					formControl: this.formControllConfigRedePactoConversas,
					title: "Modelo de Configuração da Instância WhatsApp",
					description:
						// tslint:disable-next-line:max-line-length
						"Selecione o tipo de instância para configurar a integração do WhatsApp. Escolha Individual para permitir que cada empresa gerencie sua própria instância, ou Rede para centralizar a gestão das empresas por meio de uma matriz.",
					type: ConfigItemType.SELECT,
					options: [
						{
							id: "rede",
							label: "Rede",
						},
						{
							id: "individual",
							label: "Individual",
						},
					],
					children: [
						{
							name: "chaveDeMatrizRede",
							title: "Banco Matriz da Rede",
							description: "Escolha o banco que será a Matriz da Rede",
							type: ConfigItemType.SELECT,
							formControl: this.formControlBancoDaMatrizRede,
							options: this.chavesBanco,
							hide: () =>
								this.formControllConfigRedePactoConversas.value !== "rede" ||
								this.chavesBanco.length === 0,
						},
						{
							name: "unidadeDeMatrizRede",
							title: "Selecione a unidade que será a matriz",
							description:
								"Esse é o código e o nome da unidade que será a matriz da rede.",
							type: ConfigItemType.SELECT,
							formControl: this.formControlUnidadeDeMatrizRede,
							options: this.chavesEmpresas,
							hide: () =>
								this.formControllConfigRedePactoConversas.value !== "rede" ||
								this.chavesEmpresas.length === 0 ||
								this.isCarregandoEmpresas,
						},
					],
				},
			],
		};
	}

	getInputCampanhasIa(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.IA_CAMPANHAS,
			inputs: [
				{
					typeDescription: "Adicionar Campanha",
					formControl: this.formControlCampanhaBtn,
					type: ConfigItemType.BUTTON,
					description: "Adicione uma nova campanha para a configuração de IA.",
					typeButton: "PRIMARY",
					onClick: () => {
						const modal: PactoModalRef = this.dialogService.open(
							"Adicionar nova Campanha",
							CampanhaDialogComponent,
							PactoModalSize.LARGE
						);
						modal.componentInstance.codigoConfiguracao =
							this.codigoConfiguracao || this.configuracaoIA.codigo;
						modal.componentInstance.empresa = this.empresaSelecionada;
						modal.componentInstance.idInstancia =
							this.configuracaoIA.idInstanciaZApi;
						modal.componentInstance.token = this.configuracaoIA.tokenZApi;

						modal.result.then((result) => {
							setTimeout(() => {
								this.loadCampanhaGrid();
								this.detectChanges.emit(true);
							}, 4000);
						});
					},
				},
				{
					title: "Campanhas",
					entityLogName: "configuracaocampanhacrmia",
					type: ConfigItemType.GRID,
					dataGridConfig: this.getCampanhasDataGridConfig(
						this.fromControlCampanhas
					),
					isAddRowAvailable: false,
				},
			],
		};
	}

	getCampanhasDataGridConfig(formControl: FormControl): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			dataFormControl: formControl,
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			showEdit(row, isAdd) {
				return false;
			},
			showDelete(row, isAdd) {
				return false;
			},
			endpointUrl: `${
				this.discoveryService.getUrlMap().contatoMsUrl
			}/v1/configuracao/campanhas/consultar?codigoEmpresa=${
				this.empresaSelecionada || 0
			}`,
			endpointParamsType: "query",
			formGroup: new FormGroup({
				codigo: new FormControl(),
				titulo: new FormControl(),
				tag: new FormControl(),
				descricao: new FormControl(),
				periodoinicio: new FormControl(),
				periodoFim: new FormControl(),
			}),
			columns: [
				{
					nome: "id",
					titulo: "Código",
					visible: false,
					ordenavel: true,
					editable: false,
				},
				{
					nome: "tag",
					titulo: "Tag",
					visible: true,
					ordenavel: false,
					editable: false,
					inputType: "text",
				},
				{
					nome: "titulo",
					titulo: "Titulo",
					visible: true,
					ordenavel: true,
					editable: false,
				},
				{
					nome: "descricao",
					titulo: "Instrução",
					visible: true,
					ordenavel: false,
					editable: false,
					inputType: "text",
				},
				{
					nome: "linkWhatsapp",
					titulo: "Whatsapp Link",
					visible: true,
					ordenavel: false,
					editable: false,
					inputType: "text",
					cellPointerCursor: true,
					styleClass: "pct pct-link",
				},
				{
					nome: "periodoInicial",
					titulo: "Periodo Inicial",
					visible: true,
					ordenavel: false,
					editable: false,
					valueTransform: (v) => this.formatDate(v),
				},
				{
					nome: "periodoFinal",
					titulo: "Periodo Final",
					visible: true,
					ordenavel: false,
					editable: false,
					valueTransform: (v) => this.formatDate(v),
				},
			],
			actions: [
				{
					nome: "Editar",
					iconClass: "pct pct-edit cor-action-default-able04",
					actionFn: (row) => {
						const campanha = row.row;
						const modal: PactoModalRef = this.dialogService.open(
							"Adicionar nova Campanha",
							CampanhaDialogComponent,
							PactoModalSize.LARGE
						);
						modal.componentInstance.codigoCampanha = campanha.id;
						modal.componentInstance.campanha = campanha;
						modal.componentInstance.empresa = this.empresaSelecionada;

						modal.result.then((result) => {
							setTimeout(() => {
								this.loadCampanhaGrid();
								this.detectChanges.emit(true);
							}, 4000);
						});
						return of(false);
					},
				},
				{
					nome: "Excluir",
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					actionFn: (row, rowIndex: number) => {
						const codigo = row.row.id;
						return this.apiConfigIA
							.excluirCampanha(codigo, this.empresaSelecionada)
							.subscribe(
								(res) => {
									this.notificationService.success(
										"Campanha excluída com sucesso."
									);
									this.loadCampanhaGrid();
								},
								catchError((error) => {
									this.notificationService.error("erro ao excluir campanha.");
									console.error("Error deleting campaign:", error);
									return of(false);
								})
							);
					},
				},
			],
		});
	}

	loadCampanhaGrid(): void {
		if (this.empresaSelecionada) {
			this.fromControlCampanhas.reset();
			this.apiConfigIA
				.obterCampanhas(this.empresaSelecionada)
				.subscribe((response) => {
					if (response.result && response.result.length > 0) {
						this.fromControlCampanhas.setValue({
							content: [...response.result],
						});
					} else {
						this.fromControlCampanhas.setValue({
							content: [],
						});
					}
				});
		}
	}

	loadInstrucoesGymbotGrid() {
		if (!this.empresaSelecionada) return;

		this.formControlGymbotData.setValue({ content: [] });

		this.apiConfigIA
			.obterInstrucoesGymbot(this.empresaSelecionada)
			.subscribe((response) => {
				const content =
					Array.isArray(response) && response.length > 0 ? response : [];
				this.formControlGymbotData.setValue({ content });
			});
	}

	private atualizarFasesMetaExtra(configuracaoAtualizada: any[]) {
		if (this.fasesExtraIA && this.fasesExtraIA.length > 0) {
			this.fasesExtraIA.forEach((fase) => {
				const habilitarControlName = fase.titulo + "_habilitar";
				const descricaoControlName = fase.titulo + "_descricao";

				const habilitar =
					this.formGroupConfiguracaoIA.get(habilitarControlName).value;
				const descricao =
					this.formGroupConfiguracaoIA.get(descricaoControlName).value;

				configuracaoAtualizada.push({
					fase: null,
					nomemetaextra: fase.titulo,
					codigometaextra: fase.codigo,
					habilitar,
					descricao: habilitar ? descricao : "",
				});
			});
		}
	}
	iniciarVerificacaoStatusDePDF() {
		this.mostrarOuEsconderAvisoParaPDF(true, false, null);

		if (this.intervaloPDF) {
			return;
		}

		this.intervaloPDF = setInterval(() => {
			this.verificarAtualizacaoPDF();
		}, 3000);
	}

	verificarAtualizacaoPDF() {
		if (this.consultandoPDFConversas) {
			return;
		}
		this.consultandoPDFConversas = true;
		this.apiConfigIA.consultarPDFConversas(this.empresaSelecionada).subscribe(
			(res) => {
				this.consultandoPDFConversas = false;
				this.loaderService.stopForce();
				const status = res.result[0].data.status;

				if (status === "completed") {
					this.mostrarOuEsconderAvisoParaPDF(
						false,
						true,
						res.result[0].data.url_download
					);
					this._clearIntervalPdf();
				} else if (status === "pending") {
					this.mostrarOuEsconderAvisoParaPDF(true, false, null);
					// Lógica para pending
				} else if (status === "not_found") {
					this.mostrarOuEsconderAvisoParaPDF(false, false, null);
					this._clearIntervalPdf();
					// Lógica para not_found
				} else if (status === "error") {
					this.mostrarOuEsconderAvisoParaPDF(false, false, null);
					this._clearIntervalPdf();
				}
			},
			(error) => {
				this.consultandoPDFConversas = false;
				this.notificationService.error(
					"Erro ao verificar notificações proativas: " + error.message
				);
				this._clearIntervalPdf();
			}
		);
		this.loaderService.stopForce();
	}

	private _clearIntervalPdf() {
		if (this.intervaloPDF) {
			clearInterval(this.intervaloPDF);
			this.intervaloPDF = null;
		}
	}

	mostrarOuEsconderAvisoParaPDF(
		pdfSendoProcessado: boolean,
		pdfProcessado: boolean,
		pdfUrl?: string
	) {
		const fileInput = document.querySelector(
			"pacto-cat-file-input"
		) as HTMLElement;
		if (!fileInput) {
			return;
		}
		const container = fileInput.parentElement;

		if (pdfSendoProcessado && !pdfProcessado && pdfUrl == null) {
			//remove aviso PDF anexado, botaro de remover e borao de baixar
			const avisoJaAnexado = document.getElementById("aviso-pdf-anexado");
			if (avisoJaAnexado) {
				avisoJaAnexado.remove();
			}
			const botaoRemover = document.getElementById("btn-remover-pdf");
			if (botaoRemover) {
				botaoRemover.remove();
			}
			// Esconde o aviso de PDF anexad
			const avisoAnexado = document.getElementById("aviso-processando-pdf");
			if (avisoAnexado) {
				avisoAnexado.remove();
			}
		}

		// Backup e ocultação do input original
		if (fileInput && container && (pdfSendoProcessado || pdfProcessado)) {
			if (!this.fileInputBackUp) {
				this.fileInputBackUp = fileInput;
				this.containerBackUp = container;
			}
			fileInput.style.display = "none"; // Esconde o input original
		}

		//not_found ou error, deve mostrar o input de arquivo novamente
		if (!pdfSendoProcessado && !pdfProcessado) {
			fileInput.style.display = "";
			//deve remover todos que foram criados anteriormente
			const avisoJaAnexado = document.getElementById("aviso-pdf-anexado");
			if (avisoJaAnexado) {
				avisoJaAnexado.remove();
			}
			const botaoRemover = document.getElementById("btn-remover-pdf");
			if (botaoRemover) {
				botaoRemover.remove();
			}

			const btnIconeLixeira = document.querySelector(
				"i.pct.remove.pct-trash-2"
			) as HTMLElement;
			if (btnIconeLixeira) {
				btnIconeLixeira.click();
			}
		}

		// Remove elementos auxiliares anteriores
		const avisoExistente = document.getElementById("aviso-processando-pdf");
		if (avisoExistente) {
			avisoExistente.remove();
		}

		const linkExistente = document.getElementById("link-download-pdf");
		if (linkExistente) {
			linkExistente.remove();
		}

		// Mostra aviso de processamento
		if (pdfSendoProcessado && this.containerBackUp) {
			const aviso = document.createElement("div");
			aviso.id = "aviso-processando-pdf";
			aviso.textContent = "Processando PDF...";
			aviso.style.color = "orange";
			aviso.style.marginTop = "8px";
			// if (container && !document.getElementById('aviso-pdf-anexado')) {
			if (container) {
				container.appendChild(aviso);
				// }  else if (!container && !document.getElementById('aviso-pdf-anexado'))
			} else this.containerBackUp.appendChild(aviso);
		}

		// Mostra botão de download e restaura input
		if (pdfProcessado && pdfUrl && this.containerBackUp) {
			// Força layout vertical

			if (container) {
				container.style.display = "flex";
				container.style.flexDirection = "column";
			} else {
				this.containerBackUp.style.display = "flex";
				this.containerBackUp.style.flexDirection = "column";
			}

			// Aviso de PDF anexado
			const avisoAnexado = document.createElement("div");
			avisoAnexado.id = "aviso-pdf-anexado";
			avisoAnexado.textContent = "PDF já anexado!";
			avisoAnexado.style.color = "green";
			avisoAnexado.style.marginTop = "8px";
			avisoAnexado.style.fontWeight = "bold";
			if (container && !document.getElementById("aviso-pdf-anexado"))
				container.appendChild(avisoAnexado);
			else if (!document.getElementById("aviso-pdf-anexado"))
				this.containerBackUp.appendChild(avisoAnexado);

			const link = document.createElement("a");
			link.id = "link-download-pdf";
			link.href = pdfUrl;
			link.textContent = "Baixar PDF";
			link.download = "";
			// Estilo de botão moderno
			link.style.display = "inline-block";
			link.style.background = "#007bff";
			link.style.color = "#fff";
			link.style.textDecoration = "none";
			link.style.padding = "5px 7px";
			link.style.borderRadius = "4px";
			// link.style.fontWeight = "bold";
			link.style.marginTop = "8px";
			link.style.marginRight = "0px";
			link.style.marginBottom = "12px";
			link.style.transition = "background 0.2s";
			link.onmouseover = () => (link.style.background = "#0056b3");
			link.onmouseout = () => (link.style.background = "#007bff");

			// Botão remover PDF
			const btnRemover = document.createElement("button");
			btnRemover.id = "btn-remover-pdf";
			btnRemover.textContent = "Remover PDF";
			btnRemover.style.marginLeft = "8px";
			btnRemover.style.background = "#dc3545";
			btnRemover.style.color = "#fff";
			btnRemover.style.border = "none";
			btnRemover.style.padding = "4px 12px";
			btnRemover.style.borderRadius = "4px";
			btnRemover.style.cursor = "pointer";
			btnRemover.onclick = () => {
				this.apiConfigIA.excluirPDFConversas(this.empresaSelecionada).subscribe(
					(response) => {
						this.mostrarOuEsconderAvisoParaPDF(true, false, null);
						this.iniciarVerificacaoStatusDePDF();
						link.remove();
						avisoAnexado.remove();
						btnRemover.remove();

						// Mostra novamente o input de arquivo
						if (this.fileInputBackUp) {
							this.fileInputBackUp.style.display = "block";
						}
						// Limpa o valor do formControl
						this.formControlInputFile.setValue(null);
						this.formControlInputFile.reset();
						this.detectChanges.emit(true);

						const btnIconeLixeira = document.querySelector(
							"i.pct.remove.pct-trash-2"
						) as HTMLElement;
						if (btnIconeLixeira) {
							btnIconeLixeira.click();
						}
					},
					catchError((e) => {
						this.notificationService.error(e.error.meta.message);
						return throwError(e);
					})
				);
			};

			if (container && !document.getElementById("btn-remover-pdf")) {
				container.appendChild(btnRemover);
			} else if (!document.getElementById("btn-remover-pdf")) {
				this.containerBackUp.appendChild(btnRemover);
			}
			if (container && !document.getElementById("link-download-pdf")) {
				container.appendChild(link);
			} else if (!document.getElementById("link-download-pdf")) {
				this.containerBackUp.appendChild(link);
			}
		}
		// Se não estiver mais processando e nem processado, mostra de novo o input original
		if (!pdfSendoProcessado && !pdfProcessado && this.fileInputBackUp) {
			this.fileInputBackUp.style.display = "block";
		}
	}

	savePDFIA() {
		const fileValue = this.formControlInputFile.value;
		const payload = {
			empresaId: this.empresaSelecionada,
			file:
				fileValue && fileValue.startsWith("data:application/pdf;base64,")
					? fileValue.replace("data:application/pdf;base64,", "")
					: fileValue,
		};
		this.apiConfigIA.incluirPDFConversas(payload).subscribe(
			(response) => {
				this.loaderService.stopForce();
				this.mostrarOuEsconderAvisoParaPDF(true, false, null);
				this.iniciarVerificacaoStatusDePDF();
			},
			catchError((e) => {
				this.notificationService.error(e.error.meta.message);
				return throwError(e);
			})
		);
		this.loaderService.stopForce();
	}

	saveConfiguracaoIA() {
		this.savePDFIA();
		const payload = {
			codigo: this.codigoConfiguracao,
			habilitarconfigia: this.formHabilitarPactoConversas.value,
			personalidade: this.formControlPersonalidade.value,
			informacoesAdicionaisAcademia:
				this.formControlInformacoesAdicionaisAcademia.value,
			loginPactoConversas: this.formControlLoginPactoConversas.value,
			senhaPactoConversas: this.formControlSenhaPactoConversas.value,
			tokenZApi: this.formControlTokenPactoConversas.value,
			idInstanciaZApi: this.formControlInstanciaPactoConversas.value,
			whatsappBusiness: this.formControlWhatsAppBusiness.value,
			emailResponsavelConversasAI: this.formEmailResponsavelConversasAI.value,
			telefoneResponsavelConversasAI:
				this.formTelefoneResponsavelConversasAI.value,
			horarioPadrao: this.formControlHorarioPadrao.value,
			horarioPadraoAnterior: this.horarioPadraoAnterior,
			codigoEmpresa: this.empresaSelecionada,
			habilitarGymbot: this.formHabilitarGymbot.value,
			tokenGymbot: this.formTokenGymbot.value,
			desabilitarAgendamentoAulasExperimentais:
				this.formControlDesabilitarAgendamentoAulaExperimental.value,
		};
		this.apiConfigIA.incluirConfiguracaoIA(payload).subscribe(
			(response) => {
				if (response) {
					this.configuracaoIA = response;
					this.loadConfiguracaoEmpresa(response);
					this.horarioPadraoAnterior = this.formControlHorarioPadrao.value;
				}
			},
			catchError((e) => {
				this.notificationService.error(e.error.meta.message);
				return throwError(e);
			})
		);
	}

	saveConfiguracaoFasesIA() {
		const configuracaoAtualizada = [];

		this.atualizarFases(configuracaoAtualizada);
		this.atualizarFasesMetaExtra(configuracaoAtualizada);

		this.apiConfigIA
			.incluirFases(configuracaoAtualizada, this.empresaSelecionada)
			.subscribe(
				(res) => {},
				catchError((e) => {
					this.notificationService.error(e.error.meta.message);
					return throwError(e);
				})
			);
	}

	saveConfiguracaoRedeIA() {
		const payload = {
			chaveBancoMatriz:
				this.formControlBancoDaMatrizRede.value || this.sessionService.chave,
			codigoUnidadeMatriz: this.formControlUnidadeDeMatrizRede.value,
			tipoConfigRede: this.formControllConfigRedePactoConversas.value,
		};
		this.apiConfigIA.incluirConfiguracaoRedeIA(payload).subscribe(
			(res) => {},
			catchError((e) => {
				this.notificationService.error(e.error.meta.message);
				return throwError(e);
			})
		);
	}

	copyMessage(msg) {
		if (navigator.clipboard && window.isSecureContext && document.hasFocus()) {
			navigator.clipboard
				.writeText(msg)
				.then(() => {
					this.notificationService.success("Link copiado com sucesso!");
				})
				.catch((err) => {
					console.error("Clipboard API failed", err);
					this.fallbackCopy(msg);
				});
		} else {
			this.fallbackCopy(msg);
		}
	}

	fallbackCopy(text) {
		const textarea = document.createElement("textarea");
		textarea.value = text;
		textarea.style.position = "fixed";
		textarea.style.opacity = "0";
		textarea.setAttribute("readonly", "true");
		textarea.setAttribute("aria-hidden", "true");
		document.body.appendChild(textarea);
		textarea.focus();
		textarea.select();
		try {
			const successful = document.execCommand("copy");
			if (!successful) {
				this.notificationService.error("Falha ao copiar o link!");
				return;
			}
		} catch (err) {
			this.notificationService.error("Falha ao copiar o link!");
			console.error("execCommand failed", err);
			return;
		}
		document.body.removeChild(textarea);
		this.notificationService.success("Link copiado com sucesso!");
	}

	formatDate(input) {
		const date = this.parseCustomDate(input);
		if (!date) {
			console.warn("Data inválida recebida:", input);
			return " -  - ";
		}

		const day = String(date.getUTCDate()).padStart(2, "0");
		const month = String(date.getUTCMonth() + 1).padStart(2, "0");
		const year = date.getUTCFullYear();
		return `${day}/${month}/${year}`;
	}

	parseCustomDate(input) {
		if (!input) {
			return null;
		}
		const [datePart, timePart] = input.split(" ");
		if (!datePart || !timePart) {
			return null;
		}
		const [day, month, year] = datePart.split("/");
		const [hour, minute, second] = timePart.split(":");
		const parsed = new Date(
			Date.UTC(
				parseInt(year),
				parseInt(month) - 1,
				parseInt(day),
				parseInt(hour),
				parseInt(minute),
				parseInt(second)
			)
		);
		return isNaN(parsed.getTime()) ? null : parsed;
	}
}
